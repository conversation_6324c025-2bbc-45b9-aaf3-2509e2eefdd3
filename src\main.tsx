import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ConvexReactClient } from 'convex/react'
import { ConvexAuthProvider } from '@convex-dev/auth/react'
import { createRouter, RouterProvider } from '@tanstack/react-router'
import App from './App.tsx'
import { routeTree } from './routeTree.gen'
import './index.css'

const router = createRouter({ routeTree })
const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string)

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <RouterProvider router={router} />
    <ConvexAuthProvider client={convex}>
      <App />
    </ConvexAuthProvider>
  </StrictMode>,
)
