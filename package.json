{"name": "vite-convex", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@convex-dev/auth": "^0.0.86", "@iconify-icon/react": "^3.0.0", "@tanstack/react-router": "^1.120.13", "@tanstack/react-router-devtools": "^1.120.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@tailwindcss/vite": "^4.1.8", "@tanstack/eslint-plugin-router": "^1.115.0", "@tanstack/router-plugin": "^1.120.13", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}